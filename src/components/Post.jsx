import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import PostActions from './PostActions';
import { EditableInput } from './EditableInput';
import { EditableTextarea } from './EditableTextarea';
import { useApproval } from './ApprovalProvider';

/**
 * Enhanced Post component with approval functionality and inline editing
 * @param {Object} props
 * @param {import('../types/index.js').EnhancedPost} props.post
 */
function Post({ post }) {
  const { toggleFavorite, updateNotes, updatePost } = useApproval();
  
  // Merge original content with edited content for display
  const displayContent = {
    title: post.editedContent?.title || post.title,
    caption: post.editedContent?.caption || post.caption,
    hashtags: post.editedContent?.hashtags || post.hashtags
  };

  const handleToggleFavorite = (postId) => {
    toggleFavorite(postId);
  };

  const handleUpdateNotes = (postId, notes) => {
    updateNotes(postId, notes);
  };

  const handleUpdateTitle = (newTitle) => {
    updatePost(post.id, { title: newTitle });
  };

  const handleUpdateCaption = (newCaption) => {
    updatePost(post.id, { caption: newCaption });
  };

  const handleUpdateHashtags = (newHashtags) => {
    updatePost(post.id, { hashtags: newHashtags });
  };

  return (
    <Card className={cn(
      "post-card overflow-hidden transition-all duration-200 hover:shadow-md relative",
      "hover:scale-[1.02] hover:shadow-lg shadow-sm",
      "bg-white border border-gray-200/50",
      post.isFavorited && "ring-1 ring-green-400 bg-green-50/20 shadow-green-100/50"
    )}>
      <div className="relative">
        <img 
          src={post.image} 
          alt={displayContent.title} 
          className="post-image w-full h-[300px] object-cover" 
        />
        
        {/* Edit indicator */}
        {post.isEdited && (
          <div className="absolute top-3 right-3 w-3 h-3 bg-blue-500 rounded-full shadow-sm border border-white/50"></div>
        )}

        {/* Post Actions - floating in bottom-right */}
        <PostActions
          post={post}
          onToggleFavorite={handleToggleFavorite}
          onUpdateNotes={handleUpdateNotes}
        />
      </div>
      
      <CardContent className="post-content p-6 text-left space-y-4">
        {/* Editable Title */}
        <div className="space-y-1">
          <EditableInput
            value={displayContent.title}
            onSave={handleUpdateTitle}
            placeholder="Click to edit title..."
            className="text-xl font-semibold text-gray-800"
            maxLength={100}
          />
        </div>

        {/* Editable Caption */}
        <div className="space-y-1">
          <EditableTextarea
            value={displayContent.caption}
            onSave={handleUpdateCaption}
            placeholder="Click to edit description..."
            className="text-gray-600 leading-relaxed"
            maxLength={500}
            minRows={2}
            maxRows={6}
          />
        </div>

        {/* Editable Hashtags */}
        <div className="space-y-1">
          <EditableInput
            value={displayContent.hashtags}
            onSave={handleUpdateHashtags}
            placeholder="Click to edit hashtags..."
            className="text-blue-600 text-sm"
            maxLength={200}
          />
        </div>
      </CardContent>
    </Card>
  );
}

export default Post;


