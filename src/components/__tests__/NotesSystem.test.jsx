import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import Post from '../Post';
import { ApprovalProvider, useApproval } from '../ApprovalProvider';

// Mock the UI components
vi.mock('../ui/card', () => ({
  Card: ({ children, className }) => <div className={className}>{children}</div>,
  CardContent: ({ children, className }) => <div className={className}>{children}</div>
}));

vi.mock('../ui/button', () => ({
  Button: ({ children, onClick, className, disabled, ...props }) => (
    <button 
      onClick={onClick} 
      className={className} 
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  )
}));

vi.mock('../ui/tooltip', () => ({
  Tooltip: ({ children }) => <div data-testid="tooltip">{children}</div>,
  TooltipTrigger: ({ children, asChild }) => asChild ? children : <div>{children}</div>,
  TooltipContent: ({ children }) => <div data-testid="tooltip-content">{children}</div>
}));

vi.mock('../ui/dialog', () => ({
  Dialog: ({ children, open, onOpenChange }) => 
    open ? <div data-testid="dialog" onClick={() => onOpenChange(false)}>{children}</div> : null,
  DialogContent: ({ children, className }) => <div className={className}>{children}</div>,
  DialogHeader: ({ children }) => <div>{children}</div>,
  DialogTitle: ({ children }) => <h2>{children}</h2>,
  DialogDescription: ({ children }) => <p>{children}</p>,
  DialogFooter: ({ children }) => <div>{children}</div>
}));

vi.mock('../ui/textarea', () => ({
  Textarea: ({ value, onChange, onKeyDown, placeholder, className, autoFocus, ...props }) => (
    <textarea
      value={value}
      onChange={onChange}
      onKeyDown={onKeyDown}
      placeholder={placeholder}
      className={className}
      autoFocus={autoFocus}
      {...props}
    />
  )
}));

// Mock utils
vi.mock('../../lib/utils', () => ({
  cn: (...classes) => classes.filter(Boolean).join(' ')
}));

// Mock storage
vi.mock('../../lib/storage', () => ({
  loadApprovalState: () => null,
  debouncedSaveApprovalState: vi.fn(),
  isStorageAvailable: () => true,
  checkForUnsavedChanges: () => ({ hasUnsavedChanges: false })
}));

describe('Notes System Integration', () => {
  const mockPost = {
    id: '1',
    title: 'Test Post',
    caption: 'Test caption',
    hashtags: '#test',
    image: '/test-image.jpg',
    isFavorited: false,
    notes: '',
    isEdited: false,
    editedContent: {},
    lastModified: new Date()
  };

  const renderWithProvider = (initialPosts = [mockPost]) => {
    const TestComponent = () => {
      const { posts } = useApproval();
      const postsArray = Object.values(posts);
      
      return (
        <div>
          {postsArray.map(post => (
            <Post key={post.id} post={post} />
          ))}
        </div>
      );
    };

    return render(
      <ApprovalProvider initialPosts={initialPosts}>
        <TestComponent />
      </ApprovalProvider>
    );
  };

  describe('Notes Icon Visual States', () => {
    it('shows gray notes icon for posts without notes', () => {
      renderWithProvider();
      
      const notesButton = screen.getByLabelText('Add note');
      const notesIcon = notesButton.querySelector('svg');
      expect(notesIcon).toHaveClass('text-gray-500');
    });

    it('shows orange notes icon for posts with notes', () => {
      const postWithNotes = { ...mockPost, notes: 'Test note content' };
      renderWithProvider([postWithNotes]);
      
      const notesButton = screen.getByLabelText('Edit note');
      const notesIcon = notesButton.querySelector('svg');
      expect(notesIcon).toHaveClass('text-orange-500');
    });

    it('applies orange background to notes button when post has notes', () => {
      const postWithNotes = { ...mockPost, notes: 'Test note content' };
      renderWithProvider([postWithNotes]);
      
      const notesButton = screen.getByLabelText('Edit note');
      expect(notesButton).toHaveClass('bg-orange-50/90');
    });
  });

  describe('Notes Dialog Integration', () => {
    it('opens notes dialog when notes button is clicked', () => {
      renderWithProvider();
      
      const notesButton = screen.getByLabelText('Add note');
      fireEvent.click(notesButton);
      
      expect(screen.getByTestId('dialog')).toBeInTheDocument();
      expect(screen.getByText('Add Note')).toBeInTheDocument();
    });

    it('displays existing notes in dialog', () => {
      const postWithNotes = { ...mockPost, notes: 'Existing note content' };
      renderWithProvider([postWithNotes]);
      
      const notesButton = screen.getByLabelText('Edit note');
      fireEvent.click(notesButton);
      
      const textarea = screen.getByPlaceholderText('Enter your notes here...');
      expect(textarea.value).toBe('Existing note content');
    });

    it('saves notes and updates post state', async () => {
      renderWithProvider();
      
      const notesButton = screen.getByLabelText('Add note');
      fireEvent.click(notesButton);
      
      const textarea = screen.getByPlaceholderText('Enter your notes here...');
      fireEvent.change(textarea, { target: { value: 'New note content' } });
      
      const saveButton = screen.getByText('Save Note');
      fireEvent.click(saveButton);
      
      // Wait for state update
      await waitFor(() => {
        expect(screen.getByLabelText('Edit note')).toBeInTheDocument();
      });
      
      // Check that the notes icon changed to orange
      const updatedNotesButton = screen.getByLabelText('Edit note');
      const notesIcon = updatedNotesButton.querySelector('svg');
      expect(notesIcon).toHaveClass('text-orange-500');
    });

    it('closes dialog without saving when cancel is clicked', async () => {
      const postWithNotes = { ...mockPost, notes: 'Original note' };
      renderWithProvider([postWithNotes]);
      
      const notesButton = screen.getByLabelText('Edit note');
      fireEvent.click(notesButton);
      
      const textarea = screen.getByPlaceholderText('Enter your notes here...');
      fireEvent.change(textarea, { target: { value: 'Modified note' } });
      
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);
      
      // Dialog should close
      expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
      
      // Original notes should be preserved
      const updatedNotesButton = screen.getByLabelText('Edit note');
      expect(updatedNotesButton).toBeInTheDocument();
    });
  });

  describe('Tooltip Functionality', () => {
    it('shows "Add note" tooltip for posts without notes', () => {
      renderWithProvider();
      
      const tooltipContent = screen.getAllByTestId('tooltip-content');
      expect(tooltipContent.some(content => 
        content.textContent === 'Add note'
      )).toBe(true);
    });

    it('shows note preview in tooltip for posts with notes', () => {
      const postWithNotes = { ...mockPost, notes: 'This is a test note for preview' };
      renderWithProvider([postWithNotes]);
      
      const tooltipContent = screen.getAllByTestId('tooltip-content');
      expect(tooltipContent.some(content => 
        content.textContent.includes('Note:') && 
        content.textContent.includes('This is a test note for preview')
      )).toBe(true);
    });

    it('truncates long notes in tooltip preview', () => {
      const longNote = 'This is a very long note that should be displayed in the tooltip but might need to be handled properly for display purposes';
      const postWithNotes = { ...mockPost, notes: longNote };
      renderWithProvider([postWithNotes]);
      
      const tooltipContent = screen.getAllByTestId('tooltip-content');
      expect(tooltipContent.some(content => 
        content.textContent.includes('Note:') && 
        content.textContent.includes(longNote)
      )).toBe(true);
    });
  });

  describe('Post Visual States', () => {
    it('does not apply orange styling to post card for notes (only favorites get green)', () => {
      const postWithNotes = { ...mockPost, notes: 'Test note' };
      renderWithProvider([postWithNotes]);
      
      const postCard = screen.getByRole('img').closest('.post-card');
      expect(postCard).not.toHaveClass('ring-2', 'ring-orange-400');
    });

    it('applies correct styling for post with both notes and favorite', () => {
      const postWithBoth = { ...mockPost, notes: 'Test note', isFavorited: true };
      renderWithProvider([postWithBoth]);
      
      const postCard = screen.getByRole('img').closest('.post-card');
      expect(postCard).toHaveClass('ring-1', 'ring-green-400', 'bg-green-50/20');
      
      const notesButton = screen.getByLabelText('Edit note');
      expect(notesButton).toHaveClass('bg-orange-50/90');
      
      const heartButton = screen.getByLabelText('Remove from favorites');
      expect(heartButton).toHaveClass('bg-red-50/90');
    });
  });

  describe('Keyboard Shortcuts', () => {
    it('saves notes with Ctrl+Enter', async () => {
      renderWithProvider();
      
      const notesButton = screen.getByLabelText('Add note');
      fireEvent.click(notesButton);
      
      const textarea = screen.getByPlaceholderText('Enter your notes here...');
      fireEvent.change(textarea, { target: { value: 'Keyboard shortcut test' } });
      fireEvent.keyDown(textarea, { key: 'Enter', ctrlKey: true });
      
      await waitFor(() => {
        expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
      });
      
      expect(screen.getByLabelText('Edit note')).toBeInTheDocument();
    });

    it('cancels notes editing with Escape', async () => {
      const postWithNotes = { ...mockPost, notes: 'Original note' };
      renderWithProvider([postWithNotes]);
      
      const notesButton = screen.getByLabelText('Edit note');
      fireEvent.click(notesButton);
      
      const textarea = screen.getByPlaceholderText('Enter your notes here...');
      fireEvent.change(textarea, { target: { value: 'Modified note' } });
      fireEvent.keyDown(textarea, { key: 'Escape' });
      
      await waitFor(() => {
        expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
      });
      
      // Should still show edit note (original notes preserved)
      expect(screen.getByLabelText('Edit note')).toBeInTheDocument();
    });
  });

  describe('Multiple Posts', () => {
    it('handles notes independently for multiple posts', async () => {
      const post1 = { ...mockPost, id: '1', title: 'Post 1' };
      const post2 = { ...mockPost, id: '2', title: 'Post 2' };
      renderWithProvider([post1, post2]);
      
      const notesButtons = screen.getAllByLabelText('Add note');
      expect(notesButtons).toHaveLength(2);
      
      // Add note to first post
      fireEvent.click(notesButtons[0]);
      
      const textarea = screen.getByPlaceholderText('Enter your notes here...');
      fireEvent.change(textarea, { target: { value: 'Note for post 1' } });
      
      const saveButton = screen.getByText('Save Note');
      fireEvent.click(saveButton);
      
      await waitFor(() => {
        expect(screen.getByLabelText('Edit note')).toBeInTheDocument();
        expect(screen.getByLabelText('Add note')).toBeInTheDocument();
      });
      
      // First post should have orange notes icon, second should still be gray
      const updatedButtons = screen.getAllByLabelText(/note/);
      const editButton = screen.getByLabelText('Edit note');
      const addButton = screen.getByLabelText('Add note');
      
      expect(editButton.querySelector('svg')).toHaveClass('text-orange-500');
      expect(addButton.querySelector('svg')).toHaveClass('text-gray-500');
    });
  });

  describe('Error Handling', () => {
    it('handles empty notes gracefully', async () => {
      renderWithProvider();
      
      const notesButton = screen.getByLabelText('Add note');
      fireEvent.click(notesButton);
      
      // Don't enter any text, just save
      const saveButton = screen.getByText('Save Note');
      fireEvent.click(saveButton);
      
      await waitFor(() => {
        expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
      });
      
      // Should still show "Add note" since no content was added
      expect(screen.getByLabelText('Add note')).toBeInTheDocument();
    });

    it('handles whitespace-only notes', async () => {
      renderWithProvider();
      
      const notesButton = screen.getByLabelText('Add note');
      fireEvent.click(notesButton);
      
      const textarea = screen.getByPlaceholderText('Enter your notes here...');
      fireEvent.change(textarea, { target: { value: '   \n\t   ' } });
      
      const saveButton = screen.getByText('Save Note');
      fireEvent.click(saveButton);
      
      await waitFor(() => {
        expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
      });
      
      // Should still show "Add note" since only whitespace was added
      expect(screen.getByLabelText('Add note')).toBeInTheDocument();
    });
  });
});