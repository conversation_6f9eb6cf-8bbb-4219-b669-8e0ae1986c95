import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import PostActions from '../PostActions';

// Mock the UI components
vi.mock('../ui/button', () => ({
  Button: ({ children, onClick, className, disabled, ...props }) => (
    <button 
      onClick={onClick} 
      className={className} 
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  )
}));

vi.mock('../ui/tooltip', () => ({
  Tooltip: ({ children }) => <div data-testid="tooltip">{children}</div>,
  TooltipTrigger: ({ children, asChild }) => asChild ? children : <div>{children}</div>,
  TooltipContent: ({ children }) => <div data-testid="tooltip-content">{children}</div>
}));

vi.mock('../NotesDialog', () => ({
  default: ({ open, onOpenChange, notes, onSave, postTitle }) => 
    open ? (
      <div data-testid="notes-dialog">
        <div>Notes Dialog for {postTitle}</div>
        <div>Current notes: {notes}</div>
        <button onClick={() => onSave('Test note')}>Save</button>
        <button onClick={() => onOpenChange(false)}>Close</button>
      </div>
    ) : null
}));

// Mock utils
vi.mock('../../lib/utils', () => ({
  cn: (...classes) => classes.filter(Boolean).join(' ')
}));

describe('PostActions', () => {
  const mockPost = {
    id: '1',
    title: 'Test Post',
    isFavorited: false,
    notes: ''
  };

  const mockOnToggleFavorite = vi.fn();
  const mockOnUpdateNotes = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders both heart and notes buttons', () => {
    render(
      <PostActions
        post={mockPost}
        onToggleFavorite={mockOnToggleFavorite}
        onUpdateNotes={mockOnUpdateNotes}
      />
    );

    expect(screen.getByLabelText('Add to favorites')).toBeInTheDocument();
    expect(screen.getByLabelText('Add note')).toBeInTheDocument();
  });

  it('calls onToggleFavorite when heart button is clicked', () => {
    render(
      <PostActions
        post={mockPost}
        onToggleFavorite={mockOnToggleFavorite}
        onUpdateNotes={mockOnUpdateNotes}
      />
    );

    const heartButton = screen.getByLabelText('Add to favorites');
    fireEvent.click(heartButton);

    expect(mockOnToggleFavorite).toHaveBeenCalledWith('1');
  });

  it('opens notes dialog when notes button is clicked', () => {
    render(
      <PostActions
        post={mockPost}
        onToggleFavorite={mockOnToggleFavorite}
        onUpdateNotes={mockOnUpdateNotes}
      />
    );

    const notesButton = screen.getByLabelText('Add note');
    fireEvent.click(notesButton);

    expect(screen.getByTestId('notes-dialog')).toBeInTheDocument();
  });

  it('shows favorited state when post is favorited', () => {
    const favoritedPost = { ...mockPost, isFavorited: true };
    
    render(
      <PostActions
        post={favoritedPost}
        onToggleFavorite={mockOnToggleFavorite}
        onUpdateNotes={mockOnUpdateNotes}
      />
    );

    expect(screen.getByLabelText('Remove from favorites')).toBeInTheDocument();
  });

  it('shows notes state when post has notes', () => {
    const postWithNotes = { ...mockPost, notes: 'Some notes here' };
    
    render(
      <PostActions
        post={postWithNotes}
        onToggleFavorite={mockOnToggleFavorite}
        onUpdateNotes={mockOnUpdateNotes}
      />
    );

    expect(screen.getByLabelText('Edit note')).toBeInTheDocument();
  });

  it('does not open notes dialog when disabled', () => {
    render(
      <PostActions
        post={mockPost}
        onToggleFavorite={mockOnToggleFavorite}
        onUpdateNotes={mockOnUpdateNotes}
        disabled={true}
      />
    );

    const heartButton = screen.getByLabelText('Add to favorites');
    const notesButton = screen.getByLabelText('Add note');

    fireEvent.click(heartButton);
    fireEvent.click(notesButton);

    expect(mockOnToggleFavorite).not.toHaveBeenCalled();
    expect(screen.queryByTestId('notes-dialog')).not.toBeInTheDocument();
  });

  it('calls onUpdateNotes when notes are saved', () => {
    render(
      <PostActions
        post={mockPost}
        onToggleFavorite={mockOnToggleFavorite}
        onUpdateNotes={mockOnUpdateNotes}
      />
    );

    const notesButton = screen.getByLabelText('Add note');
    fireEvent.click(notesButton);

    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);

    expect(mockOnUpdateNotes).toHaveBeenCalledWith('1', 'Test note');
  });

  it('closes notes dialog when close button is clicked', () => {
    render(
      <PostActions
        post={mockPost}
        onToggleFavorite={mockOnToggleFavorite}
        onUpdateNotes={mockOnUpdateNotes}
      />
    );

    const notesButton = screen.getByLabelText('Add note');
    fireEvent.click(notesButton);

    expect(screen.getByTestId('notes-dialog')).toBeInTheDocument();

    const closeButton = screen.getByText('Close');
    fireEvent.click(closeButton);

    expect(screen.queryByTestId('notes-dialog')).not.toBeInTheDocument();
  });

  it('displays tooltip content for notes with existing notes', () => {
    const postWithNotes = { ...mockPost, notes: 'Existing note content' };
    
    render(
      <PostActions
        post={postWithNotes}
        onToggleFavorite={mockOnToggleFavorite}
        onUpdateNotes={mockOnUpdateNotes}
      />
    );

    const tooltipContent = screen.getAllByTestId('tooltip-content');
    expect(tooltipContent.some(content => 
      content.textContent.includes('Note:') && 
      content.textContent.includes('Existing note content')
    )).toBe(true);
  });

  it('displays simple tooltip for notes without existing notes', () => {
    render(
      <PostActions
        post={mockPost}
        onToggleFavorite={mockOnToggleFavorite}
        onUpdateNotes={mockOnUpdateNotes}
      />
    );

    const tooltipContent = screen.getAllByTestId('tooltip-content');
    expect(tooltipContent.some(content => 
      content.textContent === 'Add note'
    )).toBe(true);
  });

  it('renders with tooltip wrappers', () => {
    render(
      <PostActions
        post={mockPost}
        onToggleFavorite={mockOnToggleFavorite}
        onUpdateNotes={mockOnUpdateNotes}
      />
    );

    // Check that tooltips are rendered
    const tooltips = screen.getAllByTestId('tooltip');
    expect(tooltips).toHaveLength(2); // One for heart, one for notes
  });

  it('handles missing callback functions gracefully', () => {
    render(
      <PostActions
        post={mockPost}
        onToggleFavorite={null}
        onUpdateNotes={null}
      />
    );

    const heartButton = screen.getByLabelText('Add to favorites');
    const notesButton = screen.getByLabelText('Add note');

    // Should not throw errors
    expect(() => {
      fireEvent.click(heartButton);
      fireEvent.click(notesButton);
    }).not.toThrow();
  });
});