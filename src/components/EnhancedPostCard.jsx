import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import PostActions from './PostActions';
import { EditableInput } from './EditableInput';
import { EditableTextarea } from './EditableTextarea';
import { useApproval } from './ApprovalProvider';

/**
 * Enhanced Post Card component with approval functionality and inline editing
 * Implements Google-inspired minimalist design with interactive elements
 * @param {Object} props
 * @param {import('../types/index.js').EnhancedPost} props.post
 * @param {boolean} props.isLoading - Whether the post is in loading state
 */
function EnhancedPostCard({ post, isLoading = false }) {
  const { toggleFavorite, updateNotes, updatePost } = useApproval();
  const [imageLoaded, setImageLoaded] = useState(false);
  
  // Merge original content with edited content for display
  const displayContent = {
    title: post.editedContent?.title || post.title,
    caption: post.editedContent?.caption || post.caption,
    hashtags: post.editedContent?.hashtags || post.hashtags
  };

  const handleToggleFavorite = (postId) => {
    toggleFavorite(postId);
  };

  const handleUpdateNotes = (postId, notes) => {
    updateNotes(postId, notes);
  };

  const handleUpdateTitle = (newTitle) => {
    updatePost(post.id, { title: newTitle });
  };

  const handleUpdateCaption = (newCaption) => {
    updatePost(post.id, { caption: newCaption });
  };

  const handleUpdateHashtags = (newHashtags) => {
    updatePost(post.id, { hashtags: newHashtags });
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  // Show skeleton loading state
  if (isLoading) {
    return <PostCardSkeleton />;
  }

  return (
    <Card className={cn(
      // Base card styling with Google-inspired minimalism
      "post-card overflow-hidden transition-all duration-200 relative",
      "bg-white border border-gray-200/60 shadow-sm",
      
      // Hover effects with subtle lift and scale
      "hover:shadow-md hover:scale-[1.02] hover:border-gray-300/60",
      
      // Favorite state styling with green accent
      post.isFavorited && [
        "ring-1 ring-green-400/60 bg-green-50/20",
        "shadow-green-100/50 border-green-200/60"
      ]
    )}>
      <div className="relative">
        {/* Image with loading state */}
        <div className="relative w-full h-[300px] bg-gray-100 overflow-hidden">
          {!imageLoaded && (
            <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
              <div className="w-12 h-12 border-4 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
            </div>
          )}
          <img 
            src={post.image} 
            alt={displayContent.title} 
            className={cn(
              "post-image w-full h-[300px] object-cover transition-opacity duration-300",
              imageLoaded ? "opacity-100" : "opacity-0"
            )}
            onLoad={handleImageLoad}
          />
        </div>
        
        {/* Multiple visual state indicators */}
        <div className="absolute top-3 right-3 flex gap-2 z-10">
          {/* Edit indicator - blue dot for modified content */}
          {post.isEdited && (
            <div 
              className="w-3 h-3 bg-blue-500 rounded-full shadow-sm border border-white/50 animate-pulse"
              title="This post has been edited"
            ></div>
          )}
          
          {/* Notes indicator - orange dot when post has notes */}
          {post.notes.trim().length > 0 && (
            <div 
              className="w-3 h-3 bg-orange-500 rounded-full shadow-sm border border-white/50"
              title="This post has notes"
            ></div>
          )}
          
          {/* Favorite indicator - green dot when favorited */}
          {post.isFavorited && (
            <div 
              className="w-3 h-3 bg-green-500 rounded-full shadow-sm border border-white/50"
              title="This post is favorited"
            ></div>
          )}
        </div>

        {/* Favorite state background tint overlay */}
        {post.isFavorited && (
          <div className="absolute inset-0 bg-green-500/5 pointer-events-none"></div>
        )}

        {/* Post Actions - floating in bottom-right */}
        <PostActions
          post={post}
          onToggleFavorite={handleToggleFavorite}
          onUpdateNotes={handleUpdateNotes}
        />
      </div>
      
      <CardContent className="post-content p-6 text-left space-y-4">
        {/* Editable Title */}
        <div className="space-y-1">
          <EditableInput
            value={displayContent.title}
            onSave={handleUpdateTitle}
            placeholder="Click to edit title..."
            className="text-xl font-semibold text-gray-800 leading-tight"
            maxLength={100}
          />
        </div>

        {/* Editable Caption */}
        <div className="space-y-1">
          <EditableTextarea
            value={displayContent.caption}
            onSave={handleUpdateCaption}
            placeholder="Click to edit description..."
            className="text-gray-600 leading-relaxed"
            maxLength={500}
            minRows={2}
            maxRows={6}
          />
        </div>

        {/* Editable Hashtags */}
        <div className="space-y-1">
          <EditableInput
            value={displayContent.hashtags}
            onSave={handleUpdateHashtags}
            placeholder="Click to edit hashtags..."
            className="text-blue-600 text-sm font-medium"
            maxLength={200}
          />
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Skeleton loading component for post cards
 */
function PostCardSkeleton() {
  return (
    <Card className="post-card overflow-hidden bg-white border border-gray-200/60 shadow-sm">
      <div className="relative">
        {/* Image skeleton */}
        <div className="w-full h-[300px] bg-gray-200 animate-pulse"></div>
        
        {/* Action buttons skeleton */}
        <div className="absolute bottom-3 right-3 flex gap-2">
          <div className="h-8 w-8 rounded-full bg-white/90 animate-pulse"></div>
          <div className="h-8 w-8 rounded-full bg-white/90 animate-pulse"></div>
        </div>
      </div>
      
      <CardContent className="post-content p-6 text-left space-y-4">
        {/* Title skeleton */}
        <div className="space-y-2">
          <div className="h-6 bg-gray-200 rounded animate-pulse w-3/4"></div>
        </div>

        {/* Caption skeleton */}
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 rounded animate-pulse w-full"></div>
          <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
          <div className="h-4 bg-gray-200 rounded animate-pulse w-4/6"></div>
        </div>

        {/* Hashtags skeleton */}
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3"></div>
        </div>
      </CardContent>
    </Card>
  );
}

export default EnhancedPostCard;
export { PostCardSkeleton };